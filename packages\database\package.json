{"name": "database", "version": "0.1.0", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"build": "npx tsc", "dev": "npx tsc --watch", "lint": "eslint src/", "type-check": "npx tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"firebase": "^11.8.1", "firebase-admin": "^13.4.0", "shared-types": "workspace:*", "firebase-config": "workspace:*"}, "devDependencies": {"@types/node": "^20.17.57", "typescript": "^5.8.3"}}